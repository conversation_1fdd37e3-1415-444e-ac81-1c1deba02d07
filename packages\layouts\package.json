{"name": "@saas-crm/layouts", "version": "1.0.0", "description": "Shared layout components for SaaS CRM applications", "main": "index.js", "type": "module", "exports": {".": "./index.js", "./*": "./*.jsx"}, "scripts": {"build": "echo 'No build step required for layouts'", "lint": "eslint ."}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "@saas-crm/components": "^1.0.0", "lucide-react": "^0.525.0", "clsx": "^2.1.1"}, "keywords": ["react", "layouts", "ui", "saas", "crm", "shared"], "author": "SaaS CRM Team", "license": "MIT", "private": true}