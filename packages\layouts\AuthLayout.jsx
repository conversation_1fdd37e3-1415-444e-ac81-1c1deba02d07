import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@saas-crm/components';
export const AuthLayout = ({
    children,
    title,
    description,
    className = ""
}) => {
    return (
        <div className={`min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8 ${className}`}>
            <div className="max-w-md w-full space-y-8">
                <Card className="shadow-lg">
                    {(title || description) && (
                        <CardHeader className="space-y-1 text-center">
                            {title && (
                                <CardTitle className="text-2xl font-bold tracking-tight">
                                    {title}
                                </CardTitle>
                            )}
                            {description && (
                                <CardDescription className="text-sm text-muted-foreground">
                                    {description}
                                </CardDescription>
                            )}
                        </CardHeader>
                    )}
                    <CardContent className="space-y-4">
                        {children}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};
export default AuthLayout;
