/* Admin Layout Specific Styles */
@import '../styles/global-variables.css';

.admin-layout {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Sidebar Animations */
.admin-sidebar {
  transition: width var(--transition-normal);
}

.admin-sidebar.collapsed {
  width: 4rem;
}

.admin-sidebar.expanded {
  width: 16rem;
}

/* Navigation Item Hover Effects */
.admin-nav-item {
  position: relative;
  transition: all var(--transition-fast);
}

.admin-nav-item:hover {
  transform: translateX(2px);
}

.admin-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--color-primary);
  border-radius: 0 2px 2px 0;
}

/* Header Enhancements */
.admin-header {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid var(--color-border);
}

.dark .admin-header {
  background: rgba(17, 24, 39, 0.95);
  border-bottom-color: var(--color-gray-800);
}

/* Notification Badge Animation */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Breadcrumb Styling */
.admin-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-sm);
  color: var(--color-muted-foreground);
}

.admin-breadcrumb a {
  color: var(--color-muted-foreground);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.admin-breadcrumb a:hover {
  color: var(--color-foreground);
}

.admin-breadcrumb .current {
  color: var(--color-foreground);
  font-weight: var(--font-weight-medium);
}

/* Main Content Area */
.admin-main {
  background: var(--color-background);
  min-height: 100%;
}

/* Card Enhancements for Admin */
.admin-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.admin-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Stats Cards */
.admin-stats-card {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  border: none;
}

.admin-stats-card.secondary {
  background: linear-gradient(135deg, var(--color-secondary-500), var(--color-secondary-600));
}

.admin-stats-card.success {
  background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
}

.admin-stats-card.warning {
  background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
}

.admin-stats-card.error {
  background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
}

/* Table Enhancements */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.admin-table th {
  background: var(--color-muted);
  padding: var(--spacing-lg);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--color-foreground);
  border-bottom: 1px solid var(--color-border);
}

.admin-table td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  color: var(--color-foreground);
}

.admin-table tr:hover {
  background: var(--color-muted);
}

/* Form Enhancements */
.admin-form-group {
  margin-bottom: var(--spacing-xl);
}

.admin-form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-foreground);
}

.admin-form-input {
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-card);
  color: var(--color-foreground);
  transition: all var(--transition-fast);
}

.admin-form-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button Variants for Admin */
.admin-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.admin-btn-primary {
  background: var(--color-primary);
  color: var(--color-primary-foreground);
}

.admin-btn-primary:hover {
  background: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-btn-secondary {
  background: var(--color-secondary);
  color: var(--color-secondary-foreground);
}

.admin-btn-secondary:hover {
  background: var(--color-gray-200);
}

.admin-btn-danger {
  background: var(--color-error-600);
  color: white;
}

.admin-btn-danger:hover {
  background: var(--color-error-700);
}

/* Loading States */
.admin-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-muted);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    left: -100%;
    z-index: var(--z-modal);
    transition: left var(--transition-normal);
  }
  
  .admin-sidebar.mobile-open {
    left: 0;
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .admin-header {
    padding-left: var(--spacing-lg);
  }
}

/* Dark Mode Specific Adjustments */
.dark .admin-nav-item:hover {
  background: var(--color-gray-800);
}

.dark .admin-card {
  background: var(--color-gray-900);
  border-color: var(--color-gray-800);
}

.dark .admin-table th {
  background: var(--color-gray-800);
}

.dark .admin-table tr:hover {
  background: var(--color-gray-800);
}

/* Accessibility Enhancements */
.admin-focus-visible:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .admin-sidebar,
  .admin-header {
    display: none;
  }
  
  .admin-main {
    margin: 0;
    padding: 0;
  }
}
