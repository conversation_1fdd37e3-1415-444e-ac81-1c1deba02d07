import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
    Button,
    Sheet,
    SheetContent,
    SheetTrigger,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Badge,
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@saas-crm/components';
import {
    Menu,
    Home,
    Users,
    Settings,
    LogOut,
    User,
    Bell,
    Search,
    ChevronDown,
    ChevronRight,
    Shield,
    BarChart3,
    Database,
    FileText,
    Mail,
    Calendar,
    CreditCard,
    Globe,
    Zap,
    Moon,
    Sun,
    Maximize2,
    Minimize2
} from 'lucide-react';

export const AdminLayout = ({
    navigationItems = [],
    user = { 
        name: 'Admin User', 
        email: '<EMAIL>',
        role: 'Administrator',
        avatar: null
    },
    onLogout,
    children,
    title = "Admin Dashboard",
    subtitle = "Management Portal",
    notifications = [],
    theme = 'light',
    onThemeToggle,
    sidebarCollapsed = false,
    onSidebarToggle,
    breadcrumbs = [],
    headerActions = [],
    footerContent = null,
    customStyles = {}
}) => {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [isCollapsed, setIsCollapsed] = useState(sidebarCollapsed);
    const [currentTheme, setCurrentTheme] = useState(theme);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const location = useLocation();

    // Default admin navigation items with icons and categories
    const defaultNavigationItems = [
        {
            category: 'Overview',
            items: [
                { href: '/admin/dashboard', label: 'Dashboard', icon: Home, badge: null },
                { href: '/admin/analytics', label: 'Analytics', icon: BarChart3, badge: 'New' },
            ]
        },
        {
            category: 'Management',
            items: [
                { href: '/admin/users', label: 'Users', icon: Users, badge: notifications.filter(n => n.type === 'user').length || null },
                { href: '/admin/roles', label: 'Roles & Permissions', icon: Shield, badge: null },
                { href: '/admin/content', label: 'Content', icon: FileText, badge: null },
                { href: '/admin/database', label: 'Database', icon: Database, badge: null },
            ]
        },
        {
            category: 'Communication',
            items: [
                { href: '/admin/messages', label: 'Messages', icon: Mail, badge: notifications.filter(n => n.type === 'message').length || null },
                { href: '/admin/notifications', label: 'Notifications', icon: Bell, badge: notifications.length || null },
            ]
        },
        {
            category: 'System',
            items: [
                { href: '/admin/settings', label: 'Settings', icon: Settings, badge: null },
                { href: '/admin/billing', label: 'Billing', icon: CreditCard, badge: null },
                { href: '/admin/integrations', label: 'Integrations', icon: Globe, badge: null },
                { href: '/admin/performance', label: 'Performance', icon: Zap, badge: null },
            ]
        }
    ];

    const navItems = navigationItems.length > 0 ? navigationItems : defaultNavigationItems;

    // Theme toggle handler
    const handleThemeToggle = () => {
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        setCurrentTheme(newTheme);
        if (onThemeToggle) onThemeToggle(newTheme);
        document.documentElement.classList.toggle('dark', newTheme === 'dark');
    };

    // Fullscreen toggle
    const toggleFullscreen = () => {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
            setIsFullscreen(true);
        } else {
            document.exitFullscreen();
            setIsFullscreen(false);
        }
    };

    // Sidebar collapse handler
    const handleSidebarToggle = () => {
        const newCollapsed = !isCollapsed;
        setIsCollapsed(newCollapsed);
        if (onSidebarToggle) onSidebarToggle(newCollapsed);
    };

    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement);
        };
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }, []);

    // Sidebar component with enhanced features
    const Sidebar = ({ className = "" }) => (
        <div className={`flex flex-col h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'} ${className}`}>
            {/* Header */}
            <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
                {!isCollapsed && (
                    <div className="flex flex-col">
                        <h1 className="text-lg font-bold text-gray-900 dark:text-white">{title}</h1>
                        {subtitle && <span className="text-xs text-gray-500 dark:text-gray-400">{subtitle}</span>}
                    </div>
                )}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSidebarToggle}
                    className="hidden md:flex h-8 w-8 p-0"
                >
                    {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-2 py-4 space-y-6 overflow-y-auto">
                {navItems.map((section, sectionIndex) => (
                    <div key={sectionIndex} className="space-y-2">
                        {!isCollapsed && section.category && (
                            <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                {section.category}
                            </h3>
                        )}
                        <div className="space-y-1">
                            {(section.items || section).map((item, itemIndex) => {
                                const Icon = item.icon;
                                const isActive = location.pathname === item.href;
                                const navItem = (
                                    <Link
                                        key={item.href || itemIndex}
                                        to={item.href}
                                        className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 group ${
                                            isActive
                                                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-r-2 border-blue-500'
                                                : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                                        } ${isCollapsed ? 'justify-center' : ''}`}
                                        onClick={() => setSidebarOpen(false)}
                                    >
                                        <Icon className={`h-5 w-5 ${isCollapsed ? '' : 'mr-3'} ${isActive ? 'text-blue-600 dark:text-blue-400' : ''}`} />
                                        {!isCollapsed && (
                                            <>
                                                <span className="flex-1">{item.label}</span>
                                                {item.badge && (
                                                    <Badge variant={isActive ? "default" : "secondary"} className="ml-2 text-xs">
                                                        {item.badge}
                                                    </Badge>
                                                )}
                                            </>
                                        )}
                                    </Link>
                                );

                                return isCollapsed ? (
                                    <TooltipProvider key={item.href || itemIndex}>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                {navItem}
                                            </TooltipTrigger>
                                            <TooltipContent side="right">
                                                <p>{item.label}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                ) : navItem;
                            })}
                        </div>
                    </div>
                ))}
            </nav>

            {/* Footer */}
            {!isCollapsed && footerContent && (
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                    {footerContent}
                </div>
            )}
        </div>
    );

    return (
        <div className={`flex h-screen bg-gray-50 dark:bg-gray-950 ${currentTheme === 'dark' ? 'dark' : ''}`} style={customStyles.container}>
            {/* Desktop Sidebar */}
            <div className="hidden md:flex md:flex-shrink-0">
                <Sidebar />
            </div>

            {/* Mobile Sidebar */}
            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
                <SheetContent side="left" className="p-0 w-64">
                    <Sidebar />
                </SheetContent>
            </Sheet>

            {/* Main Content */}
            <div className="flex flex-col flex-1 overflow-hidden">
                {/* Header */}
                <header className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm" style={customStyles.header}>
                    <div className="flex items-center space-x-4">
                        {/* Mobile menu button */}
                        <Sheet>
                            <SheetTrigger asChild>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="md:hidden"
                                    onClick={() => setSidebarOpen(true)}
                                >
                                    <Menu className="h-5 w-5" />
                                </Button>
                            </SheetTrigger>
                        </Sheet>

                        {/* Breadcrumbs */}
                        {breadcrumbs.length > 0 && (
                            <nav className="hidden sm:flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                                {breadcrumbs.map((crumb, index) => (
                                    <React.Fragment key={index}>
                                        {index > 0 && <span>/</span>}
                                        {crumb.href ? (
                                            <Link to={crumb.href} className="hover:text-gray-700 dark:hover:text-gray-200">
                                                {crumb.label}
                                            </Link>
                                        ) : (
                                            <span className="text-gray-900 dark:text-gray-100 font-medium">{crumb.label}</span>
                                        )}
                                    </React.Fragment>
                                ))}
                            </nav>
                        )}
                    </div>

                    {/* Header Actions */}
                    <div className="flex items-center space-x-3">
                        {/* Custom header actions */}
                        {headerActions.map((action, index) => (
                            <div key={index}>{action}</div>
                        ))}

                        {/* Search */}
                        <Button variant="ghost" size="sm" className="hidden sm:flex">
                            <Search className="h-4 w-4" />
                        </Button>

                        {/* Theme toggle */}
                        <Button variant="ghost" size="sm" onClick={handleThemeToggle}>
                            {currentTheme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                        </Button>

                        {/* Fullscreen toggle */}
                        <Button variant="ghost" size="sm" onClick={toggleFullscreen}>
                            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                        </Button>

                        {/* Notifications */}
                        <Button variant="ghost" size="sm" className="relative">
                            <Bell className="h-4 w-4" />
                            {notifications.length > 0 && (
                                <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center">
                                    {notifications.length > 9 ? '9+' : notifications.length}
                                </Badge>
                            )}
                        </Button>

                        {/* User menu */}
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                                    <Avatar className="h-9 w-9">
                                        <AvatarImage src={user.avatar} alt={user.name} />
                                        <AvatarFallback className="bg-blue-500 text-white">
                                            {user.name?.charAt(0)?.toUpperCase() || 'A'}
                                        </AvatarFallback>
                                    </Avatar>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-64" align="end" forceMount>
                                <DropdownMenuLabel className="font-normal">
                                    <div className="flex flex-col space-y-1">
                                        <p className="text-sm font-medium leading-none">{user.name}</p>
                                        <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                                        {user.role && (
                                            <Badge variant="secondary" className="w-fit text-xs mt-1">
                                                {user.role}
                                            </Badge>
                                        )}
                                    </div>
                                </DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                    <User className="mr-2 h-4 w-4" />
                                    <span>Profile</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                    <Settings className="mr-2 h-4 w-4" />
                                    <span>Settings</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={onLogout} className="text-red-600 dark:text-red-400">
                                    <LogOut className="mr-2 h-4 w-4" />
                                    <span>Log out</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </header>

                {/* Main Content Area */}
                <main className="flex-1 overflow-auto bg-gray-50 dark:bg-gray-950" style={customStyles.main}>
                    <div className="p-6">
                        {children || <Outlet />}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default AdminLayout;
