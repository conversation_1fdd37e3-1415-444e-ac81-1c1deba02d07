import React, { useState } from 'react';
import {
    Mail,
    Phone,
    MapPin,
    Clock,
    Send,
    MessageSquare,
    Users,
    Headphones
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import toast from 'react-hot-toast';
const ContactSection = () => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        inquiry_type: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const contactMethods = [
        {
            icon: Mail,
            title: 'Email Us',
            description: 'Send us an email and we\'ll respond within 24 hours',
            contact: '<EMAIL>',
            action: 'Send Email'
        },
        {
            icon: Phone,
            title: 'Call Us',
            description: 'Speak directly with our support team',
            contact: '+****************',
            action: 'Call Now'
        }
    ];
    const officeInfo = [
        {
            icon: MapPin,
            title: 'San Francisco Office',
            details: ['123 Market Street', 'San Francisco, CA 94105', 'United States']
        },
        {
            icon: Clock,
            title: 'Business Hours',
            details: ['Monday - Friday: 9:00 AM - 6:00 PM PST', 'Saturday: 10:00 AM - 4:00 PM PST', 'Sunday: Closed']
        },
        {
            icon: Users,
            title: 'Support Team',
            details: ['24/7 Technical Support', 'Dedicated Account Managers', 'Expert Implementation Team']
        }
    ];
    const inquiryTypes = [
        { value: 'sales', label: 'Sales Inquiry' },
        { value: 'support', label: 'Technical Support' },
        { value: 'partnership', label: 'Partnership' },
        { value: 'demo', label: 'Request Demo' },
        { value: 'billing', label: 'Billing Question' },
        { value: 'other', label: 'Other' }
    ];
    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!formData.name || !formData.email || !formData.message) {
            toast.error('Please fill in all required fields');
            return;
        }
        try {
            setIsSubmitting(true);
            await new Promise(resolve => setTimeout(resolve, 2000));
            toast.success('Message sent successfully! We\'ll get back to you soon.');
            setFormData({
                name: '',
                email: '',
                company: '',
                subject: '',
                message: '',
                inquiry_type: ''
            });
        } catch (error) {
            toast.error('Something went wrong. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };
    return (
        <section id="contact" className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {}
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Get in Touch
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Have questions about Accord? We're here to help. Reach out to our team
                        and we'll get back to you as soon as possible.
                    </p>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                    {}
                    {contactMethods.map((method, index) => (
                        <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                            <CardHeader>
                                <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                    <method.icon className="h-6 w-6 text-blue-600" />
                                </div>
                                <CardTitle className="text-xl">{method.title}</CardTitle>
                                <CardDescription>{method.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <p className="font-semibold text-gray-900 mb-4">{method.contact}</p>
                                <Button variant="outline" className="w-full">
                                    {method.action}
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        </section>
    );
};
export default ContactSection;
