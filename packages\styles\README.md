# @saas-crm/styles

Global CSS variables and styles for SaaS CRM projects.

## Installation

```bash
npm install @saas-crm/styles
# or
yarn add @saas-crm/styles
```

## Usage

Import the styles in your main CSS file:

```css
@import "@saas-crm/styles";
```

## Available Variables

### Spacing & Sizing
- `--spacing-xs` to `--spacing-5xl` - Consistent spacing values
- `--height-input` - Standard input height (44px)
- `--height-button-sm/md/lg/xl` - Button heights

### Colors
- Primary colors: `--color-primary-50` to `--color-primary-950`
- Secondary colors: `--color-secondary-50` to `--color-secondary-950`
- Gray colors: `--color-gray-50` to `--color-gray-950`
- Success, error, and warning color scales

### Semantic Colors
- `--color-background`, `--color-foreground`
- `--color-primary`, `--color-primary-foreground`
- `--color-card`, `--color-card-foreground`
- And many more semantic mappings

### Typography
- `--font-xs` to `--font-4xl` - Font sizes
- `--font-weight-light` to `--font-weight-bold` - Font weights

### Border Radius
- `--radius-xs` to `--radius-2xl` - Border radius values
- `--radius-full` - Full rounded borders

### Shadows
- `--shadow-xs` to `--shadow-xl` - Box shadow values

### Component Variables
- `--button-padding-x-sm/md/lg` - Button padding
- `--input-padding-x/y` - Input padding

### Transitions
- `--transition-fast/normal/slow` - Transition durations

## Customization

To customize colors for your project, simply override the CSS variables in your own CSS:

```css
:root {
  /* Override primary color */
  --color-primary-600: #your-brand-color;
  
  /* Override button height */
  --height-button-md: 3rem;
}
```

## Dark Mode

The package includes automatic dark mode support. Add the `dark` class to your root element to enable dark mode.

## Utility Classes

The package includes utility classes for common styling needs:

- `.text-primary`, `.text-secondary`, etc. - Text colors
- `.bg-primary`, `.bg-secondary`, etc. - Background colors
- `.border-primary`, `.border-secondary`, etc. - Border colors
