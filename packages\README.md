# SaaS CRM Shared Packages

This directory contains shared packages that can be used across all SaaS CRM frontend applications.

## Packages

### @saas-crm/components

Shared UI components built with shadcn/ui (using Radix UI primitives) and Tailwind CSS.

### @saas-crm/layouts

Reusable layout components for different page types.

## Installation

These packages are automatically available in the monorepo workspace. To use them in your project:

1. Add them to your `package.json` dependencies:

```json
{
  "dependencies": {
    "@saas-crm/components": "^1.0.0",
    "@saas-crm/layouts": "^1.0.0"
  }
}
```

2. Install dependencies:

```bash
yarn install
```

## Usage

### Components

Import individual components from the shared package:

```jsx
import { Button, Card, Input, Label } from "@saas-crm/components";

function MyComponent() {
  return (
    <Card>
      <Label htmlFor="email">Email</Label>
      <Input id="email" type="email" />
      <Button>Submit</Button>
    </Card>
  );
}
```

### Layouts

Import and use layout components:

```jsx
import { AuthLayout, DashboardLayout, MainLayout } from "@saas-crm/layouts";

// For authentication pages
function LoginPage() {
  return (
    <AuthLayout title="Sign In" description="Welcome back">
      {/* Your form content */}
    </AuthLayout>
  );
}

// For dashboard pages
function Dashboard() {
  const navigationItems = [
    { href: "/dashboard", label: "Dashboard", icon: Home },
    { href: "/users", label: "Users", icon: Users },
  ];

  return (
    <DashboardLayout
      navigationItems={navigationItems}
      user={currentUser}
      onLogout={handleLogout}
      title="My App"
    >
      {/* Your dashboard content */}
    </DashboardLayout>
  );
}

// For general pages
function HomePage() {
  return <MainLayout title="My App">{/* Your page content */}</MainLayout>;
}
```

## Available Components

### UI Components

- Accordion
- Alert & AlertDialog
- AspectRatio
- Badge
- Breadcrumb
- Button
- Card
- Carousel
- Checkbox
- Collapsible
- Command
- ContextMenu
- Dialog
- DropdownMenu
- Form
- HoverCard
- Input
- Label
- Menubar
- NavigationMenu
- Pagination
- Popover
- RadioGroup
- Resizable
- ScrollArea
- Select
- Separator
- Sheet
- Slider
- Switch
- Table
- Tabs
- Textarea
- Toggle & ToggleGroup
- Tooltip

### Layout Components

#### AuthLayout

A centered layout for authentication pages.

**Props:**

- `children` - The content to render
- `title` - Title to display in the card header
- `description` - Description to display in the card header
- `className` - Additional CSS classes

#### DashboardLayout

A layout with sidebar navigation for dashboard pages.

**Props:**

- `navigationItems` - Array of navigation items `{ href, label, icon }`
- `user` - User object with name, email, avatar
- `onLogout` - Function to handle logout
- `children` - Content to render (optional, uses Outlet if not provided)
- `title` - Application title

#### MainLayout

A simple layout with header and footer for general pages.

**Props:**

- `children` - Content to render (optional, uses Outlet if not provided)
- `title` - Application title
- `headerLinks` - Array of header navigation links `{ href, label }`
- `showFooter` - Whether to show the footer
- `className` - Additional CSS classes for main container

## Development

### Adding New Components

1. Create the component in the appropriate package directory
2. Export it from the package's `index.js` file
3. Update this documentation

### Styling

All components use Tailwind CSS for styling. Make sure your consuming applications have Tailwind CSS configured with the same design tokens.

## Examples

See the example files in the individual project directories:

- `Saas-Crm-Auth-Frontend/src/pages/LoginWithSharedLayout.jsx`
- `Saas-Crm-Auth-Frontend/src/pages/DashboardExample.jsx`
