import React, { use, useEffect, useState } from 'react';
import {
    Package,
    DollarSign,
    CheckCircle,
    Star
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import Header from '../components/Header';
import Hero from '../components/Hero';
import Footer from '../components/Footer';
import ContactSection from '../components/ContactSection';
import PlanSelectionModal from '../components/PlanSelectionModal';
import apiClient from '@/lib/apiClient';
const SubscriptionRequest = () => {
    const [selectedPackage, setSelectedPackage] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [allPackages, setAllPackages] = useState([]);
    const getAllPackages = () => {
        apiClient.get('/packages').then((res) => {
            setAllPackages(res.data)
        })
    }
    useEffect(() => {
        getAllPackages();
    }, [])
    const packages = [
        {
            id: 1,
            name: 'Starter',
            description: 'Perfect for small businesses getting started with CRM',
            price: 29,
            duration_days: 30,
            modules: ['Contacts Management', 'Basic Sales Pipeline', 'Email Support', 'Mobile App Access', 'Basic Reports']
        },
        {
            id: 2,
            name: 'Professional',
            description: 'Advanced features for growing businesses',
            price: 79,
            duration_days: 30,
            modules: ['Everything in Starter', 'Advanced Sales Tools', 'Marketing Automation', 'Live Chat Support', 'Custom Fields', 'Advanced Analytics']
        },
        {
            id: 3,
            name: 'Enterprise',
            description: 'Complete solution for large organizations',
            price: 149,
            duration_days: 30,
            modules: ['Everything in Professional', 'Custom Workflows', 'API Access', 'Priority Support', 'Advanced Security', 'Dedicated Account Manager']
        }
    ];
    const handleGetStarted = () => {
        setIsModalOpen(true);
    };
    const handleSelectPlan = (pkg) => {
        setSelectedPackage(pkg);
        setIsModalOpen(true);
    };
    const getPopularBadge = (pkg) => {
        return pkg.name === 'Professional';
    };
    return (
        <div className="min-h-screen bg-white">
            <Header />
            <Hero onGetStarted={handleGetStarted} />
            <section id="pricing" className="py-20 bg-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                            Choose Your Plan
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            Select the perfect plan for your business and get started with our powerful CRM system.
                            All plans include a 14-day free trial.
                        </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-stretch">
                        {allPackages.map((pkg, index) => (
                            <Card
                                key={index}
                                className="relative transition-all duration-200 hover:shadow-lg hover:scale-105 flex flex-col min-h-[500px]"
                            >
                                <CardHeader className="text-center">
                                    <div className="flex items-center justify-center mb-2">
                                        <Package className="h-8 w-8 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-2xl">{pkg.name}</CardTitle>
                                </CardHeader>
                                <CardContent className="flex flex-col h-full">
                                    <div className="text-center mb-6">
                                        <div className="flex items-center justify-center mb-2">
                                            <DollarSign className="h-6 w-6 text-green-600" />
                                            <span className="text-4xl font-bold">{pkg.price}</span>
                                            <span className="text-gray-500 ml-2">
                                                {pkg.duration_days} days
                                            </span>
                                        </div>
                                    </div>
                                    <div className="space-y-3 mb-6 flex-grow">
                                        <h4 className="font-semibold text-gray-900">Included Features:</h4>
                                        <div className={`space-y-2 pr-2 ${pkg.modules && pkg.modules.length > 5
                                            ? 'max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3'
                                            : ''
                                            }`}
                                            style={pkg.modules && pkg.modules.length > 5 ? {
                                                scrollbarWidth: 'thin',
                                                scrollbarColor: '#cbd5e1 transparent'
                                            } : {}}>
                                            {pkg.modules?.map((module, index) => (
                                                <div key={index} className="flex items-center">
                                                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                                    <span className="text-sm text-gray-700">{module?.name || module} Module</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    <div className="mt-auto">
                                        <Button
                                            className="w-full bg-blue-600 hover:bg-blue-700"
                                            onClick={() => handleSelectPlan(pkg)}
                                        >
                                            Select Plan
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </section>
            <ContactSection />
            <Footer />
            <PlanSelectionModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                selectedPackage={selectedPackage}
                packages={packages}
            />
        </div>
    );
};
export default SubscriptionRequest;
