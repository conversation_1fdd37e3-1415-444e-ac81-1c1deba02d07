# AdminLayout - Dynamic Admin Dashboard Layout

A beautiful, feature-rich admin dashboard layout component that can be reused across multiple microservices repositories like Saas-Crm-User-Front and others.

## Features

### 🎨 Beautiful Design
- Modern, clean interface with consistent styling
- Dark/Light theme support with smooth transitions
- Responsive design that works on all devices
- Customizable color schemes using CSS variables

### 🚀 Dynamic & Flexible
- Collapsible sidebar with tooltips
- Customizable navigation items with categories
- Breadcrumb navigation support
- Fullscreen mode toggle
- Mobile-responsive with slide-out sidebar

### 🔧 Admin-Specific Features
- User profile dropdown with role badges
- Notification system with badges
- Quick search functionality
- System status indicators
- Recent activity tracking
- Stats cards with trend indicators

### 🎯 Developer Friendly
- TypeScript-ready with prop validation
- Extensive customization options
- Built-in accessibility features
- Print-friendly styles
- Easy integration with any routing system

## Installation

```bash
# Install the layouts package
npm install @saas-crm/layouts

# Or if using yarn
yarn add @saas-crm/layouts
```

## Basic Usage

```jsx
import React from 'react';
import { AdminLayout } from '@saas-crm/layouts';
import { Home, Users, Settings } from 'lucide-react';

function App() {
  const navigationItems = [
    {
      category: 'Main',
      items: [
        { href: '/admin/dashboard', label: 'Dashboard', icon: Home },
        { href: '/admin/users', label: 'Users', icon: Users, badge: 5 },
        { href: '/admin/settings', label: 'Settings', icon: Settings },
      ]
    }
  ];

  const user = {
    name: 'John Admin',
    email: '<EMAIL>',
    role: 'Super Admin',
    avatar: '/path/to/avatar.jpg'
  };

  return (
    <AdminLayout
      title="My Admin Panel"
      subtitle="Management Portal"
      navigationItems={navigationItems}
      user={user}
      onLogout={() => console.log('Logout clicked')}
    >
      {/* Your admin content goes here */}
      <h1>Welcome to Admin Dashboard</h1>
    </AdminLayout>
  );
}
```

## Advanced Usage

```jsx
import React, { useState } from 'react';
import { 
  AdminLayout, 
  AdminStatsCard, 
  AdminQuickActions,
  AdminRecentActivity,
  AdminPageHeader 
} from '@saas-crm/layouts';

function AdminDashboard() {
  const [theme, setTheme] = useState('light');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const notifications = [
    { type: 'user', message: 'New user registered' },
    { type: 'system', message: 'System update available' }
  ];

  const breadcrumbs = [
    { label: 'Admin', href: '/admin' },
    { label: 'Dashboard' }
  ];

  const headerActions = [
    <button key="export" className="btn btn-primary">Export Data</button>
  ];

  return (
    <AdminLayout
      title="SaaS CRM Admin"
      subtitle="Management Dashboard"
      user={{
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'Administrator'
      }}
      notifications={notifications}
      theme={theme}
      onThemeToggle={setTheme}
      sidebarCollapsed={sidebarCollapsed}
      onSidebarToggle={setSidebarCollapsed}
      breadcrumbs={breadcrumbs}
      headerActions={headerActions}
      onLogout={() => window.location.href = '/login'}
      customStyles={{
        container: { fontFamily: 'Inter, sans-serif' },
        header: { backgroundColor: 'rgba(255,255,255,0.95)' }
      }}
    >
      <AdminPageHeader
        title="Dashboard Overview"
        subtitle="Monitor your system performance and user activity"
        breadcrumbs={breadcrumbs}
        actions={headerActions}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <AdminStatsCard
          title="Total Users"
          value="1,234"
          change="+12%"
          changeType="positive"
          color="primary"
        />
        <AdminStatsCard
          title="Revenue"
          value="$45,678"
          change="+8%"
          changeType="positive"
          color="success"
        />
        <AdminStatsCard
          title="Orders"
          value="567"
          change="-3%"
          changeType="negative"
          color="warning"
        />
        <AdminStatsCard
          title="Active Sessions"
          value="89"
          change="+15%"
          changeType="positive"
          color="secondary"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AdminQuickActions />
        <AdminRecentActivity />
      </div>
    </AdminLayout>
  );
}
```

## Props Reference

### AdminLayout Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `navigationItems` | `Array` | `[]` | Navigation menu items with categories |
| `user` | `Object` | `{}` | User information object |
| `title` | `String` | `"Admin Dashboard"` | Main title in sidebar |
| `subtitle` | `String` | `"Management Portal"` | Subtitle in sidebar |
| `notifications` | `Array` | `[]` | Notification items |
| `theme` | `String` | `'light'` | Theme mode ('light' or 'dark') |
| `onThemeToggle` | `Function` | `null` | Theme toggle callback |
| `sidebarCollapsed` | `Boolean` | `false` | Sidebar collapsed state |
| `onSidebarToggle` | `Function` | `null` | Sidebar toggle callback |
| `breadcrumbs` | `Array` | `[]` | Breadcrumb navigation items |
| `headerActions` | `Array` | `[]` | Custom header action components |
| `footerContent` | `ReactNode` | `null` | Custom footer content |
| `customStyles` | `Object` | `{}` | Custom CSS styles |
| `onLogout` | `Function` | `null` | Logout callback function |
| `children` | `ReactNode` | `null` | Main content |

### Navigation Item Structure

```javascript
{
  category: 'Category Name', // Optional category grouping
  items: [
    {
      href: '/path',
      label: 'Menu Item',
      icon: IconComponent,
      badge: 'New' // Optional badge text or number
    }
  ]
}
```

## Component Utilities

### AdminStatsCard
Display key metrics with trend indicators.

### AdminQuickActions
Quick action buttons for common admin tasks.

### AdminRecentActivity
Show recent system activity with user avatars.

### AdminSystemStatus
Display system health and uptime information.

### AdminPageHeader
Consistent page headers with breadcrumbs and actions.

### AdminDataTable
Feature-rich data table with loading states.

## Styling & Customization

The AdminLayout uses CSS variables from the global styles package for consistent theming:

```css
/* Custom theme colors */
:root {
  --color-admin-primary: #3b82f6;
  --color-admin-secondary: #8b5cf6;
  --admin-sidebar-width: 16rem;
  --admin-sidebar-collapsed-width: 4rem;
}
```

## Integration Examples

### With React Router
```jsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AdminLayout } from '@saas-crm/layouts';

function App() {
  return (
    <BrowserRouter>
      <AdminLayout>
        <Routes>
          <Route path="/admin/dashboard" element={<Dashboard />} />
          <Route path="/admin/users" element={<Users />} />
          <Route path="/admin/settings" element={<Settings />} />
        </Routes>
      </AdminLayout>
    </BrowserRouter>
  );
}
```

### With Next.js
```jsx
// pages/_app.js
import { AdminLayout } from '@saas-crm/layouts';
import '@saas-crm/layouts/AdminLayout.css';

function MyApp({ Component, pageProps }) {
  return (
    <AdminLayout>
      <Component {...pageProps} />
    </AdminLayout>
  );
}
```

## Best Practices

1. **Consistent Navigation**: Use the same navigation structure across all admin pages
2. **Responsive Design**: Test on mobile devices and tablets
3. **Accessibility**: Ensure keyboard navigation and screen reader support
4. **Performance**: Lazy load heavy components and data
5. **Security**: Implement proper role-based access control
6. **Theming**: Use CSS variables for consistent styling

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

When contributing to the AdminLayout:

1. Follow the existing code style
2. Add TypeScript types for new props
3. Update documentation for new features
4. Test on multiple screen sizes
5. Ensure accessibility compliance

## License

MIT License - see LICENSE file for details.
