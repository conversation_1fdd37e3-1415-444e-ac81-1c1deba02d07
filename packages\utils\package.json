{"name": "@saas-crm/utils", "version": "1.0.0", "description": "Shared utilities and API client for SaaS CRM applications", "main": "index.js", "type": "module", "exports": {".": "./index.js", "./api": "./api/index.js", "./validation": "./validation/index.js", "./storage": "./storage/index.js", "./format": "./format/index.js"}, "scripts": {"build": "echo 'No build step required for utils'", "lint": "eslint ."}, "peerDependencies": {"axios": "^1.10.0", "react-hot-toast": "^2.5.2"}, "keywords": ["utils", "api", "validation", "saas", "crm", "shared"], "author": "SaaS CRM Team", "license": "MIT", "private": true}