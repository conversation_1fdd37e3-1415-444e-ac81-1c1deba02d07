import React from 'react';
import { Badge } from '@saas-crm/components';
import { 
    TrendingUp, 
    TrendingDown, 
    Users, 
    DollarSign, 
    ShoppingCart, 
    Activity 
} from 'lucide-react';

// Stats Card Component
export const AdminStatsCard = ({ 
    title, 
    value, 
    change, 
    changeType = 'positive', 
    icon: Icon = Activity,
    color = 'primary',
    className = '' 
}) => {
    const colorClasses = {
        primary: 'bg-gradient-to-r from-blue-500 to-blue-600',
        secondary: 'bg-gradient-to-r from-purple-500 to-purple-600',
        success: 'bg-gradient-to-r from-green-500 to-green-600',
        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
        error: 'bg-gradient-to-r from-red-500 to-red-600',
    };

    return (
        <div className={`p-6 rounded-lg text-white shadow-lg hover:shadow-xl transition-all duration-200 ${colorClasses[color]} ${className}`}>
            <div className="flex items-center justify-between">
                <div className="flex-1">
                    <p className="text-sm font-medium opacity-90">{title}</p>
                    <p className="text-3xl font-bold mt-2">{value}</p>
                    {change && (
                        <div className="flex items-center mt-2">
                            {changeType === 'positive' ? (
                                <TrendingUp className="h-4 w-4 mr-1" />
                            ) : (
                                <TrendingDown className="h-4 w-4 mr-1" />
                            )}
                            <span className="text-sm font-medium">{change}</span>
                        </div>
                    )}
                </div>
                <div className="ml-4">
                    <Icon className="h-8 w-8 opacity-80" />
                </div>
            </div>
        </div>
    );
};

// Quick Actions Component
export const AdminQuickActions = ({ actions = [] }) => {
    const defaultActions = [
        { label: 'Add User', href: '/admin/users/new', color: 'primary' },
        { label: 'View Reports', href: '/admin/reports', color: 'secondary' },
        { label: 'System Settings', href: '/admin/settings', color: 'warning' },
        { label: 'Backup Data', href: '/admin/backup', color: 'success' },
    ];

    const actionItems = actions.length > 0 ? actions : defaultActions;

    return (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-3">
                {actionItems.map((action, index) => (
                    <a
                        key={index}
                        href={action.href}
                        className={`p-3 rounded-lg text-center text-sm font-medium transition-all duration-200 hover:shadow-md ${
                            action.color === 'primary' ? 'bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300' :
                            action.color === 'secondary' ? 'bg-purple-50 text-purple-700 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300' :
                            action.color === 'success' ? 'bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-300' :
                            action.color === 'warning' ? 'bg-yellow-50 text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-300' :
                            'bg-gray-50 text-gray-700 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300'
                        }`}
                    >
                        {action.label}
                    </a>
                ))}
            </div>
        </div>
    );
};

// Recent Activity Component
export const AdminRecentActivity = ({ activities = [] }) => {
    const defaultActivities = [
        { user: 'John Doe', action: 'created a new user account', time: '2 minutes ago', type: 'user' },
        { user: 'Jane Smith', action: 'updated system settings', time: '15 minutes ago', type: 'system' },
        { user: 'Mike Johnson', action: 'generated monthly report', time: '1 hour ago', type: 'report' },
        { user: 'Sarah Wilson', action: 'backed up database', time: '2 hours ago', type: 'backup' },
        { user: 'Tom Brown', action: 'deleted inactive users', time: '3 hours ago', type: 'user' },
    ];

    const activityItems = activities.length > 0 ? activities : defaultActivities;

    const getActivityColor = (type) => {
        switch (type) {
            case 'user': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
            case 'system': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
            case 'report': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
            case 'backup': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
        }
    };

    return (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
            <div className="space-y-4">
                {activityItems.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                                    {activity.user.charAt(0)}
                                </span>
                            </div>
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-900 dark:text-white">
                                <span className="font-medium">{activity.user}</span> {activity.action}
                            </p>
                            <div className="flex items-center mt-1 space-x-2">
                                <Badge className={`text-xs ${getActivityColor(activity.type)}`}>
                                    {activity.type}
                                </Badge>
                                <span className="text-xs text-gray-500 dark:text-gray-400">{activity.time}</span>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// System Status Component
export const AdminSystemStatus = ({ status = {} }) => {
    const defaultStatus = {
        server: { status: 'online', uptime: '99.9%', color: 'success' },
        database: { status: 'online', uptime: '99.8%', color: 'success' },
        cache: { status: 'warning', uptime: '98.5%', color: 'warning' },
        storage: { status: 'online', uptime: '99.9%', color: 'success' },
    };

    const systemStatus = Object.keys(status).length > 0 ? status : defaultStatus;

    const getStatusColor = (color) => {
        switch (color) {
            case 'success': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
            case 'warning': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
            case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
            default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
        }
    };

    return (
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Status</h3>
            <div className="space-y-3">
                {Object.entries(systemStatus).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className={`h-3 w-3 rounded-full ${
                                value.color === 'success' ? 'bg-green-500' :
                                value.color === 'warning' ? 'bg-yellow-500' :
                                value.color === 'error' ? 'bg-red-500' : 'bg-gray-500'
                            }`}></div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                                {key}
                            </span>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Badge className={`text-xs ${getStatusColor(value.color)}`}>
                                {value.status}
                            </Badge>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                {value.uptime}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// Admin Page Header Component
export const AdminPageHeader = ({ 
    title, 
    subtitle, 
    actions = [], 
    breadcrumbs = [],
    className = '' 
}) => {
    return (
        <div className={`mb-8 ${className}`}>
            {breadcrumbs.length > 0 && (
                <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
                    {breadcrumbs.map((crumb, index) => (
                        <React.Fragment key={index}>
                            {index > 0 && <span>/</span>}
                            {crumb.href ? (
                                <a href={crumb.href} className="hover:text-gray-700 dark:hover:text-gray-200">
                                    {crumb.label}
                                </a>
                            ) : (
                                <span className="text-gray-900 dark:text-gray-100 font-medium">{crumb.label}</span>
                            )}
                        </React.Fragment>
                    ))}
                </nav>
            )}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{title}</h1>
                    {subtitle && (
                        <p className="mt-2 text-gray-600 dark:text-gray-400">{subtitle}</p>
                    )}
                </div>
                {actions.length > 0 && (
                    <div className="flex items-center space-x-3">
                        {actions.map((action, index) => (
                            <div key={index}>{action}</div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

// Admin Data Table Component
export const AdminDataTable = ({ 
    columns = [], 
    data = [], 
    loading = false,
    emptyMessage = "No data available",
    className = '' 
}) => {
    if (loading) {
        return (
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-3 text-gray-600 dark:text-gray-400">Loading...</span>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden ${className}`}>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            {columns.map((column, index) => (
                                <th
                                    key={index}
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                                >
                                    {column.header}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {data.length === 0 ? (
                            <tr>
                                <td colSpan={columns.length} className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                    {emptyMessage}
                                </td>
                            </tr>
                        ) : (
                            data.map((row, rowIndex) => (
                                <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                    {columns.map((column, colIndex) => (
                                        <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                            {column.render ? column.render(row[column.key], row) : row[column.key]}
                                        </td>
                                    ))}
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default {
    AdminStatsCard,
    AdminQuickActions,
    AdminRecentActivity,
    AdminSystemStatus,
    AdminPageHeader,
    AdminDataTable
};
