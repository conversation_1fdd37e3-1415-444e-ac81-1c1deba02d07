:root {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.25rem;
  --spacing-2xl: 1.5rem;
  --spacing-3xl: 2rem;
  --spacing-4xl: 2.5rem;
  --spacing-5xl: 3rem;
  --height-input: 2.75rem;
  --height-button-sm: 2rem;
  --height-button-md: 2.5rem;
  --height-button-lg: 3rem;
  --height-button-xl: 3.5rem;
  --radius-xs: 0.25rem;
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-2xl: 1.5rem;
  --font-3xl: 1.875rem;
  --font-4xl: 2.25rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;
  --color-secondary-950: #3b0764;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  --color-background: var(--color-gray-50);
  --color-foreground: var(--color-gray-900);
  --color-card: #ffffff;
  --color-card-foreground: var(--color-gray-900);
  --color-popover: #ffffff;
  --color-popover-foreground: var(--color-gray-900);
  --color-primary: var(--color-primary-600);
  --color-primary-foreground: #ffffff;
  --color-secondary: var(--color-gray-100);
  --color-secondary-foreground: var(--color-gray-900);
  --color-muted: var(--color-gray-100);
  --color-muted-foreground: var(--color-gray-500);
  --color-accent: var(--color-gray-100);
  --color-accent-foreground: var(--color-gray-900);
  --color-destructive: var(--color-error-600);
  --color-destructive-foreground: #ffffff;
  --color-border: var(--color-gray-200);
  --color-input: var(--color-gray-200);
  --color-ring: var(--color-primary-600);
  --button-padding-x-sm: var(--spacing-md);
  --button-padding-x-md: var(--spacing-lg);
  --button-padding-x-lg: var(--spacing-xl);
  --button-padding-y: var(--spacing-sm);
  --input-padding-x: var(--spacing-md);
  --input-padding-y: var(--spacing-sm);
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}
.dark {
  --color-background: var(--color-gray-950);
  --color-foreground: var(--color-gray-50);
  --color-card: var(--color-gray-900);
  --color-card-foreground: var(--color-gray-50);
  --color-popover: var(--color-gray-900);
  --color-popover-foreground: var(--color-gray-50);
  --color-primary: var(--color-primary-500);
  --color-primary-foreground: var(--color-gray-950);
  --color-secondary: var(--color-gray-800);
  --color-secondary-foreground: var(--color-gray-50);
  --color-muted: var(--color-gray-800);
  --color-muted-foreground: var(--color-gray-400);
  --color-accent: var(--color-gray-800);
  --color-accent-foreground: var(--color-gray-50);
  --color-destructive: var(--color-error-500);
  --color-destructive-foreground: var(--color-gray-50);
  --color-border: var(--color-gray-800);
  --color-input: var(--color-gray-800);
  --color-ring: var(--color-primary-500);
}
.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary-600); }
.text-success { color: var(--color-success-600); }
.text-error { color: var(--color-error-600); }
.text-warning { color: var(--color-warning-600); }
.bg-primary { background-color: var(--color-primary); }
.bg-secondary { background-color: var(--color-secondary-600); }
.bg-success { background-color: var(--color-success-600); }
.bg-error { background-color: var(--color-error-600); }
.bg-warning { background-color: var(--color-warning-600); }
.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary-600); }
.border-success { border-color: var(--color-success-600); }
.border-error { border-color: var(--color-error-600); }
.border-warning { border-color: var(--color-warning-600); }
