import * as React from "react"
import { cn } from "../lib/utils"
function Card({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card"
            className={cn(
                "bg-[var(--color-card)] text-[var(--color-card-foreground)] flex flex-col gap-[var(--spacing-xl)] rounded-[var(--radius-xl)] border border-[var(--color-border)] py-[var(--spacing-xl)] shadow-[var(--shadow-sm)]",
                className
            )}
            {...props} />
    );
}
function CardHeader({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card-header"
            className={cn(
                "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-[var(--spacing-sm)] px-[var(--spacing-xl)] has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-[var(--spacing-xl)]",
                className
            )}
            {...props} />
    );
}
function CardTitle({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card-title"
            className={cn("leading-none font-[var(--font-weight-semibold)] text-[var(--font-lg)]", className)}
            {...props} />
    );
}
function CardDescription({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card-description"
            className={cn("text-[var(--color-muted-foreground)] text-[var(--font-sm)]", className)}
            {...props} />
    );
}
function CardAction({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card-action"
            className={cn(
                "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
                className
            )}
            {...props} />
    );
}
function CardContent({
    className,
    ...props
}) {
    return (<div data-slot="card-content" className={cn("px-[var(--spacing-xl)]", className)} {...props} />);
}
function CardFooter({
    className,
    ...props
}) {
    return (
        <div
            data-slot="card-footer"
            className={cn("flex items-center px-[var(--spacing-xl)] [.border-t]:pt-[var(--spacing-xl)]", className)}
            {...props} />
    );
}
export {
    Card,
    CardHeader,
    CardFooter,
    CardTitle,
    CardAction,
    CardDescription,
    CardContent,
}
