import React from 'react';
import { ArrowR<PERSON>, Play, CheckCircle, Users, Building, TrendingUp } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
const Hero = ({ onGetStarted }) => {
    const features = [
        'Complete CRM Solution',
        'Advanced Analytics',
        'Team Collaboration',
        '24/7 Support'
    ];
    const stats = [
        { icon: Users, value: '10,000+', label: 'Happy Customers' },
        { icon: Building, value: '500+', label: 'Companies' },
        { icon: TrendingUp, value: '99.9%', label: 'Uptime' }
    ];
    return (
        <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-16 pb-20 overflow-hidden">
            {}
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-0 left-0 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
            <div className="absolute top-0 right-0 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
            <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                    {}
                    <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                        Transform Your Business with{' '}
                        <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                            Accord CRM
                        </span>
                    </h1>
                    {}
                    <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                        The all-in-one customer relationship management platform that helps you
                        <strong className="text-gray-900"> grow faster</strong>,
                        <strong className="text-gray-900"> sell smarter</strong>, and
                        <strong className="text-gray-900"> serve better</strong>.
                    </p>
                    {}
                    <div className="flex flex-wrap justify-center gap-4 mb-10">
                        {features.map((feature, index) => (
                            <div key={index} className="flex items-center bg-white rounded-full px-4 py-2 shadow-sm border border-gray-100">
                                <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                <span className="text-sm font-medium text-gray-700">{feature}</span>
                            </div>
                        ))}
                    </div>
                    {}
                    <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                        <Button
                            size="lg"
                            className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-4 h-auto"
                            onClick={onGetStarted}
                        >
                            Start Free Trial
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                        <Button
                            variant="outline"
                            size="lg"
                            className="text-lg px-8 py-4 h-auto border-2"
                        >
                            <Play className="mr-2 h-5 w-5" />
                            Watch Demo
                        </Button>
                    </div>
                    {}
                    <div className="text-sm text-gray-500 mb-8">
                        <p>Trusted by industry leaders • No credit card required • 14-day free trial</p>
                    </div>
                    {}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
                        {stats.map((stat, index) => (
                            <div key={index} className="text-center">
                                <div className="flex justify-center mb-2">
                                    <div className="p-3 bg-blue-100 rounded-full">
                                        <stat.icon className="h-6 w-6 text-blue-600" />
                                    </div>
                                </div>
                                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                                <div className="text-sm text-gray-600">{stat.label}</div>
                            </div>
                        ))}
                    </div>
                </div>
                {}
                <div className="mt-16 relative">
                    <div className="relative mx-auto max-w-5xl">
                        <div className="relative rounded-2xl shadow-2xl overflow-hidden bg-white border border-gray-200">
                            {}
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <div className="flex items-center space-x-4">
                                    <div className="flex space-x-2">
                                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                                        <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                                    </div>
                                    <div className="flex-1 bg-white rounded-md px-3 py-1 text-sm text-gray-500">
                                        app.accord.com/dashboard
                                    </div>
                                </div>
                            </div>
                            {}
                            <div className="p-6 bg-gradient-to-br from-blue-50 to-purple-50 min-h-96">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                                    <div className="bg-white p-4 rounded-lg shadow-sm">
                                        <div className="text-2xl font-bold text-blue-600">2,847</div>
                                        <div className="text-sm text-gray-600">Total Contacts</div>
                                    </div>
                                    <div className="bg-white p-4 rounded-lg shadow-sm">
                                        <div className="text-2xl font-bold text-green-600">$127K</div>
                                        <div className="text-sm text-gray-600">Revenue This Month</div>
                                    </div>
                                    <div className="bg-white p-4 rounded-lg shadow-sm">
                                        <div className="text-2xl font-bold text-purple-600">94%</div>
                                        <div className="text-sm text-gray-600">Customer Satisfaction</div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-lg shadow-sm p-6">
                                    <div className="h-48 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                                        <div className="text-center">
                                            <div className="text-lg font-semibold text-gray-700 mb-2">
                                                Interactive Dashboard
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                Real-time analytics and insights
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {}
                        <div className="absolute -top-4 -left-4 w-24 h-24 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
                        <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-purple-500 rounded-full opacity-20 animate-pulse animation-delay-1000"></div>
                    </div>
                </div>
            </div>
            <style jsx>{`
                @keyframes blob {
                    0% { transform: translate(0px, 0px) scale(1); }
                    33% { transform: translate(30px, -50px) scale(1.1); }
                    66% { transform: translate(-20px, 20px) scale(0.9); }
                    100% { transform: translate(0px, 0px) scale(1); }
                }
                .animate-blob {
                    animation: blob 7s infinite;
                }
                .animation-delay-2000 {
                    animation-delay: 2s;
                }
                .animation-delay-4000 {
                    animation-delay: 4s;
                }
                .animation-delay-1000 {
                    animation-delay: 1s;
                }
            `}</style>
        </section>
    );
};
export default Hero;
