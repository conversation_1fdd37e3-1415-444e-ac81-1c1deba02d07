import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Mail, Lock, User, UserPlus, Sparkles } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Checkbox } from '../components/ui/checkbox';
import { useAuth } from '../contexts/AuthContext';
import { validation } from '../lib/utils';
const registerSchema = z.object({
    firstName: z
        .string()
        .min(1, 'First name is required')
        .min(2, 'First name must be at least 2 characters')
        .max(50, 'First name must be less than 50 characters'),
    lastName: z
        .string()
        .min(1, 'Last name is required')
        .min(2, 'Last name must be at least 2 characters')
        .max(50, 'Last name must be less than 50 characters'),
    email: z
        .string()
        .min(1, 'Email is required')
        .refine((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email), {
            message: 'Please enter a valid email address',
        }),
    password: z
        .string()
        .min(1, 'Password is required')
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    confirmPassword: z
        .string()
        .min(1, 'Please confirm your password'),
    agreeToTerms: z
        .boolean()
        .refine(val => val === true, 'You must agree to the terms and conditions'),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
const Register = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState({ strength: 'weak', feedback: [] });
    const { register: registerUser, isLoading, error, isAuthenticated, clearError } = useAuth();
    const navigate = useNavigate();
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/dashboard', { replace: true });
        }
    }, [isAuthenticated, navigate]);
    useEffect(() => {
        clearError();
    }, [clearError]);
    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isSubmitting },
        watch,
        setError,
    } = useForm({
        resolver: zodResolver(registerSchema),
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            password: '',
            confirmPassword: '',
            agreeToTerms: false,
        },
    });
    const watchedPassword = watch('password');
    useEffect(() => {
        if (watchedPassword) {
            const strength = validation.passwordStrength(watchedPassword);
            setPasswordStrength(strength);
        }
    }, [watchedPassword]);
    const onSubmit = async (data) => {
        try {
            const result = await registerUser({
                first_name: data.firstName,
                last_name: data.lastName,
                name: `${data.firstName} ${data.lastName}`,
                email: data.email,
                password: data.password,
                password_confirmation: data.confirmPassword,
            });
            if (!result.success) {
                if (result.error?.includes('email')) {
                    setError('email', { message: result.error });
                } else if (result.error?.includes('password')) {
                    setError('password', { message: result.error });
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
        }
    };
    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };
    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };
    const getPasswordStrengthColor = (strength) => {
        switch (strength) {
            case 'weak': return 'bg-red-500';
            case 'medium': return 'bg-yellow-500';
            case 'strong': return 'bg-blue-500';
            case 'very-strong': return 'bg-green-500';
            default: return 'bg-gray-300';
        }
    };
    const getPasswordStrengthWidth = (strength) => {
        switch (strength) {
            case 'weak': return 'w-1/4';
            case 'medium': return 'w-2/4';
            case 'strong': return 'w-3/4';
            case 'very-strong': return 'w-full';
            default: return 'w-0';
        }
    };
    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-blue-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                        Accord
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        Create your account to get started.
                    </p>
                </div>
                <Card className="mt-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <CardContent className="space-y-6">
                            {error && (
                                <Alert variant="destructive" className="border-red-200 bg-red-50">
                                    <AlertDescription className="text-red-800">{error}</AlertDescription>
                                </Alert>
                            )}
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">First Name</Label>
                                    <div className="relative">
                                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                        <Input
                                            id="firstName"
                                            type="text"
                                            placeholder="First name"
                                            className="pl-10 h-11 border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                                            {...register('firstName')}
                                            disabled={isLoading || isSubmitting}
                                        />
                                    </div>
                                    {errors.firstName && (
                                        <p className="text-sm text-red-600">{errors.firstName.message}</p>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">Last Name</Label>
                                    <div className="relative">
                                        <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                        <Input
                                            id="lastName"
                                            type="text"
                                            placeholder="Last name"
                                            className="pl-10 h-11 border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                                            {...register('lastName')}
                                            disabled={isLoading || isSubmitting}
                                        />
                                    </div>
                                    {errors.lastName && (
                                        <p className="text-sm text-red-600">{errors.lastName.message}</p>
                                    )}
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email Address</Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        className="pl-10 h-11 border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                                        {...register('email')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-600">{errors.email.message}</p>
                                )}
                            </div>
                            {}
                            <div className="space-y-2">
                                <Label htmlFor="password">Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Create a password"
                                        className="pl-10 pr-10"
                                        {...register('password')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={togglePasswordVisibility}
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {}
                                {watchedPassword && (
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2">
                                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                                                <div
                                                    className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength.strength)} ${getPasswordStrengthWidth(passwordStrength.strength)}`}
                                                ></div>
                                            </div>
                                            <span className="text-xs text-gray-600 capitalize">
                                                {passwordStrength.strength.replace('-', ' ')}
                                            </span>
                                        </div>
                                        {passwordStrength.feedback.length > 0 && (
                                            <div className="text-xs text-gray-600">
                                                Missing: {passwordStrength.feedback.join(', ')}
                                            </div>
                                        )}
                                    </div>
                                )}
                                {errors.password && (
                                    <p className="text-sm text-red-600">{errors.password.message}</p>
                                )}
                            </div>
                            {}
                            <div className="space-y-2">
                                <Label htmlFor="confirmPassword">Confirm Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="confirmPassword"
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        placeholder="Confirm your password"
                                        className="pl-10 pr-10"
                                        {...register('confirmPassword')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={toggleConfirmPasswordVisibility}
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showConfirmPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.confirmPassword && (
                                    <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
                                )}
                            </div>
                            {}
                            <div className="flex items-start space-x-2">
                                <Controller
                                    name="agreeToTerms"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox
                                            id="agreeToTerms"
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                            disabled={isLoading || isSubmitting}
                                        />
                                    )}
                                />
                                <div className="grid gap-1.5 leading-none">
                                    <Label
                                        htmlFor="agreeToTerms"
                                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                        I agree to the{' '}
                                        <Link to="/terms" className="text-primary hover:text-primary/80">
                                            Terms of Service
                                        </Link>{' '}
                                        and{' '}
                                        <Link to="/privacy" className="text-primary hover:text-primary/80">
                                            Privacy Policy
                                        </Link>
                                    </Label>
                                    {errors.agreeToTerms && (
                                        <p className="text-sm text-red-600">{errors.agreeToTerms.message}</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col space-y-4 pt-6">
                            <Button
                                type="submit"
                                className="w-full h-11 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
                                disabled={isLoading || isSubmitting}
                            >
                                {isLoading || isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Creating Account...
                                    </>
                                ) : (
                                    <>
                                        <UserPlus className="mr-2 h-4 w-4" />
                                        Create Account
                                    </>
                                )}
                            </Button>
                            <div className="text-center text-sm">
                                <span className="text-gray-600">Already have an account? </span>
                                <Link
                                    to="/login"
                                    className="text-purple-600 hover:text-purple-800 font-medium transition-colors"
                                >
                                    Sign in here
                                </Link>
                            </div>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </div>
    );
};
export default Register;
