import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle, Sparkles } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription } from '../components/ui/alert';
import { useAuth } from '../contexts/AuthContext';
import { validation } from '../lib/utils';
const resetPasswordSchema = z.object({
    password: z
        .string()
        .min(1, 'Password is required')
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    confirmPassword: z
        .string()
        .min(1, 'Please confirm your password'),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
const ResetPassword = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState({ strength: 'weak', feedback: [] });
    const [isSuccess, setIsSuccess] = useState(false);
    const [tokenError, setTokenError] = useState(null);
    const { resetPassword, isLoading, error, isAuthenticated, clearError } = useAuth();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const token = searchParams.get('token');
    const email = searchParams.get('email');
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/dashboard', { replace: true });
        }
    }, [isAuthenticated, navigate]);
    useEffect(() => {
        if (!token) {
            setTokenError('Invalid or missing reset token. Please request a new password reset.');
        }
    }, [token]);
    useEffect(() => {
        clearError();
    }, [clearError]);
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        watch,
        setError,
    } = useForm({
        resolver: zodResolver(resetPasswordSchema),
        defaultValues: {
            password: '',
            confirmPassword: '',
        },
    });
    const watchedPassword = watch('password');
    useEffect(() => {
        if (watchedPassword) {
            const strength = validation.passwordStrength(watchedPassword);
            setPasswordStrength(strength);
        }
    }, [watchedPassword]);
    const onSubmit = async (data) => {
        if (!token) {
            setTokenError('Invalid reset token. Please request a new password reset.');
            return;
        }
        try {
            const result = await resetPassword(token, data.password, data.confirmPassword);
            if (result.success) {
                setIsSuccess(true);
            } else {
                if (result.error?.includes('password')) {
                    setError('password', { message: result.error });
                } else if (result.error?.includes('token')) {
                    setTokenError(result.error);
                }
            }
        } catch (error) {
            console.error('Reset password error:', error);
        }
    };
    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };
    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };
    const getPasswordStrengthColor = (strength) => {
        switch (strength) {
            case 'weak': return 'bg-red-500';
            case 'medium': return 'bg-yellow-500';
            case 'strong': return 'bg-blue-500';
            case 'very-strong': return 'bg-green-500';
            default: return 'bg-gray-300';
        }
    };
    const getPasswordStrengthWidth = (strength) => {
        switch (strength) {
            case 'weak': return 'w-1/4';
            case 'medium': return 'w-2/4';
            case 'strong': return 'w-3/4';
            case 'very-strong': return 'w-full';
            default: return 'w-0';
        }
    };
    if (isSuccess) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Accord
                        </h1>
                        <p className="mt-2 text-sm text-gray-600">
                            Welcome back! Please sign in to your account.
                        </p>
                    </div>
                    <Card className="mt-8">
                        <CardHeader className="space-y-1 text-center">
                            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <CheckCircle className="w-6 h-6 text-green-600" />
                            </div>
                            <CardTitle className="text-2xl font-bold">Password Reset Successful</CardTitle>
                            <CardDescription>
                                Your password has been successfully reset. You can now sign in with your new password.
                            </CardDescription>
                        </CardHeader>
                        <CardFooter>
                            <Button
                                onClick={() => navigate('/login')}
                                className="w-full"
                            >
                                Continue to Sign In
                            </Button>
                        </CardFooter>
                    </Card>
                </div>
            </div>
        );
    }
    if (tokenError) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div className="text-center">
                        <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Accord
                        </h1>
                        <p className="mt-2 text-sm text-gray-600">
                            Welcome back! Please sign in to your account.
                        </p>
                    </div>
                    <Card className="mt-8">
                        <CardHeader className="space-y-1 text-center">
                            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <AlertCircle className="w-6 h-6 text-red-600" />
                            </div>
                            <CardTitle className="text-2xl font-bold">Invalid Reset Link</CardTitle>
                            <CardDescription>
                                This password reset link is invalid or has expired.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Alert variant="destructive">
                                <AlertDescription>{tokenError}</AlertDescription>
                            </Alert>
                        </CardContent>
                        <CardFooter className="flex flex-col space-y-3">
                            <Button
                                onClick={() => navigate('/forgot-password')}
                                className="w-full"
                            >
                                Request New Reset Link
                            </Button>
                            <Button
                                onClick={() => navigate('/login')}
                                variant="outline"
                                className="w-full"
                            >
                                Back to Sign In
                            </Button>
                        </CardFooter>
                    </Card>
                </div>
            </div>
        );
    }
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900">
                        {import.meta.env.VITE_APP_NAME || 'SaaS CRM'}
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        Create a new password for your account.
                    </p>
                </div>
                <Card className="mt-8">
                    <CardHeader className="space-y-1">
                        <CardTitle className="text-2xl font-bold text-center">Reset Password</CardTitle>
                        <CardDescription className="text-center">
                            {email ? `Resetting password for ${email}` : 'Enter your new password below'}
                        </CardDescription>
                    </CardHeader>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <CardContent className="space-y-4">
                            {}
                            {error && (
                                <Alert variant="destructive">
                                    <AlertDescription>{error}</AlertDescription>
                                </Alert>
                            )}
                            {}
                            <div className="space-y-2">
                                <Label htmlFor="password">New Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Enter your new password"
                                        className="pl-10 pr-10"
                                        {...register('password')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={togglePasswordVisibility}
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {}
                                {watchedPassword && (
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2">
                                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                                                <div
                                                    className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor(passwordStrength.strength)} ${getPasswordStrengthWidth(passwordStrength.strength)}`}
                                                ></div>
                                            </div>
                                            <span className="text-xs text-gray-600 capitalize">
                                                {passwordStrength.strength.replace('-', ' ')}
                                            </span>
                                        </div>
                                        {passwordStrength.feedback.length > 0 && (
                                            <div className="text-xs text-gray-600">
                                                Missing: {passwordStrength.feedback.join(', ')}
                                            </div>
                                        )}
                                    </div>
                                )}
                                {errors.password && (
                                    <p className="text-sm text-red-600">{errors.password.message}</p>
                                )}
                            </div>
                            {}
                            <div className="space-y-2">
                                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="confirmPassword"
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        placeholder="Confirm your new password"
                                        className="pl-10 pr-10"
                                        {...register('confirmPassword')}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={toggleConfirmPasswordVisibility}
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showConfirmPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.confirmPassword && (
                                    <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
                                )}
                            </div>
                            {}
                            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                                <div className="flex">
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-blue-800">
                                            Password Security Tips
                                        </h3>
                                        <div className="mt-2 text-sm text-blue-700">
                                            <ul className="list-disc list-inside space-y-1">
                                                <li>Use at least 8 characters</li>
                                                <li>Include uppercase and lowercase letters</li>
                                                <li>Add numbers and special characters</li>
                                                <li>Avoid common words or personal information</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col space-y-4">
                            <Button
                                type="submit"
                                className="w-full"
                                disabled={isLoading || isSubmitting}
                            >
                                {isLoading || isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Resetting Password...
                                    </>
                                ) : (
                                    <>
                                        <Lock className="mr-2 h-4 w-4" />
                                        Reset Password
                                    </>
                                )}
                            </Button>
                            {}
                            <div className="text-center text-sm">
                                <span className="text-gray-600">Remember your password? </span>
                                <Link
                                    to="/login"
                                    className="text-primary hover:text-primary/80 font-medium"
                                >
                                    Sign in here
                                </Link>
                            </div>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </div>
    );
};
export default ResetPassword;
