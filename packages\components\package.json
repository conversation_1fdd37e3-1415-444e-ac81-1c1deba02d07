{"name": "@saas-crm/components", "version": "1.0.0", "description": "Shared shadcn/ui components for SaaS CRM applications", "main": "index.js", "type": "module", "exports": {".": "./index.js", "./ui/*": "./ui/*.jsx"}, "scripts": {"build": "echo 'No build step required for components'", "lint": "eslint ."}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.14", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "lucide-react": "^0.525.0", "cmdk": "^1.0.4", "embla-carousel-react": "^8.5.2", "react-hook-form": "^7.56.2"}, "keywords": ["react", "components", "ui", "saas", "crm", "shared"], "author": "SaaS CRM Team", "license": "MIT", "private": true}