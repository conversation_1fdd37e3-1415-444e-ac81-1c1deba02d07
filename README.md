# SaaS CRM Frontend

A comprehensive Customer Relationship Management (CRM) system built with modern web technologies. This monorepo contains multiple frontend applications and shared packages for a complete SaaS CRM solution.

## 🏗️ Architecture

This repository follows a monorepo structure with multiple frontend applications and shared packages:

```
├── Saas-Crm-Auth-Frontend/          # Authentication & User Management
├── Saas-Crm-Subscription-Frontend/  # Subscription & Billing Management
├── Saas-Crm-User-Frontend/         # Main CRM Dashboard & Features
└── packages/                        # Shared Components & Utilities
    ├── components/                  # Reusable UI Components
    ├── layouts/                     # Layout Components
    ├── styles/                      # Global Styles & Variables
    └── utils/                       # Shared Utilities
```

## 🚀 Applications

### Authentication Frontend

**Location:** `Saas-Crm-Auth-Frontend/`

Handles user authentication, registration, password management, and user profile operations.

**Features:**

- User login and registration
- Password reset functionality
- Profile management
- Session handling
- Protected routes

### Subscription Frontend

**Location:** `Saas-Crm-Subscription-Frontend/`

Manages subscription plans, billing, and payment processing for the SaaS platform.

**Features:**

- Subscription plan selection
- Payment processing
- Billing history
- Plan upgrades/downgrades
- Invoice management

### User Frontend

**Location:** `Saas-Crm-User-Frontend/`

The main CRM application providing core customer relationship management features.

**Features:**

- Customer management
- Contact organization
- Sales pipeline
- Dashboard analytics
- Reporting tools

## 📦 Shared Packages

### Components Package

**Location:** `packages/components/`

A comprehensive UI component library built with React and styled with Tailwind CSS.

**Includes:**

- Form components (Input, Button, Select, etc.)
- Layout components (Card, Dialog, Sheet, etc.)
- Navigation components (Menu, Breadcrumb, etc.)
- Data display components (Table, Badge, Avatar, etc.)

### Layouts Package

**Location:** `packages/layouts/`

Reusable layout components for consistent application structure.

**Components:**

- `AuthLayout` - Centered layout for authentication pages
- `DashboardLayout` - Sidebar navigation layout for dashboard pages
- `MainLayout` - Header/footer layout for general pages

### Styles Package

**Location:** `packages/styles/`

Global CSS variables, utility classes, and design system definitions.

**Features:**

- CSS custom properties for colors, spacing, typography
- Dark mode support
- Consistent design tokens
- Utility classes for common styles

### Utils Package

**Location:** `packages/utils/`

Shared utility functions and helpers used across applications.

**Includes:**

- API client configuration
- Form validation helpers
- Data formatting utilities
- Storage management

## 🛠️ Technology Stack

- **Framework:** React 18 with Vite
- **Styling:** Tailwind CSS with custom CSS variables
- **UI Components:** Radix UI primitives
- **Routing:** React Router v6
- **Forms:** React Hook Form with validation
- **HTTP Client:** Axios
- **Notifications:** React Hot Toast
- **Icons:** Lucide React
- **Build Tool:** Vite
- **Package Manager:** Yarn Workspaces

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- Yarn package manager

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd Saas-CRM-Frontend
```

2. Install dependencies:

```bash
yarn install
```

3. Set up environment variables for each application:

```bash
# Copy environment templates
cp Saas-Crm-Auth-Frontend/.env.example Saas-Crm-Auth-Frontend/.env
cp Saas-Crm-Subscription-Frontend/.env.example Saas-Crm-Subscription-Frontend/.env
cp Saas-Crm-User-Frontend/.env.example Saas-Crm-User-Frontend/.env
```

4. Start development servers:

```bash
# Start all applications
yarn dev

# Or start individual applications
yarn workspace saas-crm-auth-frontend dev
yarn workspace saas-crm-subscription-frontend dev
yarn workspace saas-crm-user-frontend dev
```

## 🔧 Development

### Project Structure

Each frontend application follows a consistent structure:

```
src/
├── components/          # Application-specific components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── pages/              # Page components
└── styles/             # Application-specific styles
```

### Shared Components Usage

Import shared components from the packages:

```jsx
import { Button, Card, Input } from "@saas-crm/components";
import { AuthLayout } from "@saas-crm/layouts";
```

### Environment Variables

Each application supports the following environment variables:

```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_NAME=SaaS CRM
VITE_APP_BASENAME=/
VITE_DEBUG=false
```

## 🏃‍♂️ Available Scripts

### Root Level Scripts

```bash
yarn dev          # Start all applications in development mode
yarn build        # Build all applications for production
yarn preview      # Preview production builds
yarn lint         # Run ESLint on all packages
```

### Individual Application Scripts

```bash
yarn workspace <app-name> dev      # Start specific app in development
yarn workspace <app-name> build    # Build specific app for production
yarn workspace <app-name> preview  # Preview specific app production build
```

## 🎨 Design System

The project uses a comprehensive design system with:

- **Colors:** Primary, secondary, success, error, warning palettes
- **Typography:** Consistent font sizes and weights
- **Spacing:** Standardized spacing scale
- **Components:** Consistent component sizing and styling
- **Dark Mode:** Full dark mode support

All design tokens are defined in `packages/styles/global-variables.css`.

## 🔐 Authentication Flow

1. User accesses protected route
2. Redirected to Auth Frontend if not authenticated
3. After successful login, redirected back to original destination
4. JWT token stored in sessionStorage
5. API requests include Authorization header
6. Token refresh handled automatically

## 📱 Responsive Design

All applications are fully responsive with:

- Mobile-first approach
- Breakpoint-based layouts
- Touch-friendly interactions
- Optimized for all screen sizes

## 🚀 Deployment

### Production Build

```bash
yarn build
```

This creates optimized production builds in each application's `dist/` directory.

### Environment Setup

Ensure production environment variables are configured:

- API endpoints
- Application names and branding
- Feature flags
- Analytics tracking IDs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Run tests and linting: `yarn lint`
5. Commit your changes: `git commit -m 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

### Code Style

- Use TypeScript for new components when possible
- Follow existing naming conventions
- Add proper JSDoc comments for complex functions
- Ensure responsive design for all new components
- Test components across different screen sizes

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Create an issue in this repository
- Check the documentation in each application's README
- Review the shared component documentation

## 🔄 Version History

- **v1.0.0** - Initial release with basic CRM functionality
- **v1.1.0** - Added subscription management
- **v1.2.0** - Enhanced UI components and layouts
- **v1.3.0** - Improved authentication flow and security
