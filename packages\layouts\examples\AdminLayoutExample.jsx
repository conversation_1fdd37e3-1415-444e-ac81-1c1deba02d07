import React, { useState } from 'react';
import { 
    AdminLayout, 
    AdminStatsCard, 
    AdminQuickActions,
    AdminRecentActivity,
    AdminSystemStatus,
    AdminPageHeader,
    AdminDataTable
} from '../index.js';
import {
    Home,
    Users,
    Settings,
    BarChart3,
    Shield,
    Database,
    FileText,
    Mail,
    Bell,
    Calendar,
    CreditCard,
    Globe,
    Zap,
    TrendingUp,
    DollarSign,
    ShoppingCart,
    Activity
} from 'lucide-react';

// Example Admin Dashboard Component
export const AdminDashboardExample = () => {
    const [theme, setTheme] = useState('light');
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

    // Sample navigation items
    const navigationItems = [
        {
            category: 'Overview',
            items: [
                { href: '/admin/dashboard', label: 'Dashboard', icon: Home, badge: null },
                { href: '/admin/analytics', label: 'Analytics', icon: BarChart3, badge: 'New' },
            ]
        },
        {
            category: 'Management',
            items: [
                { href: '/admin/users', label: 'Users', icon: Users, badge: 12 },
                { href: '/admin/roles', label: 'Roles & Permissions', icon: Shield, badge: null },
                { href: '/admin/content', label: 'Content', icon: FileText, badge: 3 },
                { href: '/admin/database', label: 'Database', icon: Database, badge: null },
            ]
        },
        {
            category: 'Communication',
            items: [
                { href: '/admin/messages', label: 'Messages', icon: Mail, badge: 5 },
                { href: '/admin/notifications', label: 'Notifications', icon: Bell, badge: 8 },
                { href: '/admin/calendar', label: 'Calendar', icon: Calendar, badge: null },
            ]
        },
        {
            category: 'System',
            items: [
                { href: '/admin/settings', label: 'Settings', icon: Settings, badge: null },
                { href: '/admin/billing', label: 'Billing', icon: CreditCard, badge: null },
                { href: '/admin/integrations', label: 'Integrations', icon: Globe, badge: 2 },
                { href: '/admin/performance', label: 'Performance', icon: Zap, badge: null },
            ]
        }
    ];

    // Sample user data
    const user = {
        name: 'John Administrator',
        email: '<EMAIL>',
        role: 'Super Administrator',
        avatar: null
    };

    // Sample notifications
    const notifications = [
        { type: 'user', message: 'New user registered', time: '2 min ago' },
        { type: 'system', message: 'System update available', time: '1 hour ago' },
        { type: 'message', message: 'New support ticket', time: '3 hours ago' },
        { type: 'backup', message: 'Daily backup completed', time: '1 day ago' },
        { type: 'security', message: 'Security scan completed', time: '2 days ago' },
    ];

    // Sample breadcrumbs
    const breadcrumbs = [
        { label: 'Admin', href: '/admin' },
        { label: 'Dashboard' }
    ];

    // Sample header actions
    const headerActions = [
        <button key="export" className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Export Data
        </button>,
        <button key="refresh" className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            Refresh
        </button>
    ];

    // Sample quick actions
    const quickActions = [
        { label: 'Add New User', href: '/admin/users/new', color: 'primary' },
        { label: 'Generate Report', href: '/admin/reports/new', color: 'secondary' },
        { label: 'System Backup', href: '/admin/backup', color: 'success' },
        { label: 'Security Scan', href: '/admin/security/scan', color: 'warning' },
    ];

    // Sample recent activities
    const recentActivities = [
        { user: 'Alice Johnson', action: 'created a new user account for Bob Smith', time: '5 minutes ago', type: 'user' },
        { user: 'Mike Chen', action: 'updated system configuration settings', time: '20 minutes ago', type: 'system' },
        { user: 'Sarah Wilson', action: 'generated monthly analytics report', time: '1 hour ago', type: 'report' },
        { user: 'David Brown', action: 'completed database backup process', time: '2 hours ago', type: 'backup' },
        { user: 'Emma Davis', action: 'resolved 3 support tickets', time: '4 hours ago', type: 'support' },
    ];

    // Sample system status
    const systemStatus = {
        server: { status: 'online', uptime: '99.9%', color: 'success' },
        database: { status: 'online', uptime: '99.8%', color: 'success' },
        cache: { status: 'warning', uptime: '98.5%', color: 'warning' },
        storage: { status: 'online', uptime: '99.9%', color: 'success' },
        api: { status: 'online', uptime: '99.7%', color: 'success' },
    };

    // Sample table data
    const tableColumns = [
        { key: 'name', header: 'Name' },
        { key: 'email', header: 'Email' },
        { key: 'role', header: 'Role' },
        { key: 'status', header: 'Status', render: (value) => (
            <span className={`px-2 py-1 rounded-full text-xs ${
                value === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
                {value}
            </span>
        )},
        { key: 'lastLogin', header: 'Last Login' }
    ];

    const tableData = [
        { name: 'John Doe', email: '<EMAIL>', role: 'Admin', status: 'active', lastLogin: '2 hours ago' },
        { name: 'Jane Smith', email: '<EMAIL>', role: 'User', status: 'active', lastLogin: '1 day ago' },
        { name: 'Bob Johnson', email: '<EMAIL>', role: 'Manager', status: 'inactive', lastLogin: '1 week ago' },
    ];

    const handleLogout = () => {
        console.log('Logout clicked');
        // Implement logout logic here
        alert('Logout functionality would be implemented here');
    };

    const handleThemeToggle = (newTheme) => {
        setTheme(newTheme);
        console.log('Theme changed to:', newTheme);
    };

    const handleSidebarToggle = (collapsed) => {
        setSidebarCollapsed(collapsed);
        console.log('Sidebar collapsed:', collapsed);
    };

    return (
        <AdminLayout
            title="SaaS CRM Admin"
            subtitle="Management Portal"
            navigationItems={navigationItems}
            user={user}
            notifications={notifications}
            theme={theme}
            onThemeToggle={handleThemeToggle}
            sidebarCollapsed={sidebarCollapsed}
            onSidebarToggle={handleSidebarToggle}
            breadcrumbs={breadcrumbs}
            headerActions={headerActions}
            onLogout={handleLogout}
            customStyles={{
                container: { fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif' },
                header: { backdropFilter: 'blur(8px)' }
            }}
            footerContent={
                <div className="text-xs text-gray-500 text-center">
                    © 2024 SaaS CRM. All rights reserved.
                </div>
            }
        >
            {/* Page Header */}
            <AdminPageHeader
                title="Dashboard Overview"
                subtitle="Monitor your system performance and user activity"
                breadcrumbs={breadcrumbs}
                actions={headerActions}
            />

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <AdminStatsCard
                    title="Total Users"
                    value="2,847"
                    change="+12.5%"
                    changeType="positive"
                    icon={Users}
                    color="primary"
                />
                <AdminStatsCard
                    title="Monthly Revenue"
                    value="$45,678"
                    change="+8.2%"
                    changeType="positive"
                    icon={DollarSign}
                    color="success"
                />
                <AdminStatsCard
                    title="Active Orders"
                    value="1,234"
                    change="-3.1%"
                    changeType="negative"
                    icon={ShoppingCart}
                    color="warning"
                />
                <AdminStatsCard
                    title="System Load"
                    value="67%"
                    change="+15.3%"
                    changeType="positive"
                    icon={Activity}
                    color="secondary"
                />
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Quick Actions */}
                <div className="lg:col-span-1">
                    <AdminQuickActions actions={quickActions} />
                </div>

                {/* Recent Activity */}
                <div className="lg:col-span-1">
                    <AdminRecentActivity activities={recentActivities} />
                </div>

                {/* System Status */}
                <div className="lg:col-span-1">
                    <AdminSystemStatus status={systemStatus} />
                </div>
            </div>

            {/* Data Table */}
            <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Users</h3>
                <AdminDataTable
                    columns={tableColumns}
                    data={tableData}
                    loading={false}
                    emptyMessage="No users found"
                />
            </div>

            {/* Additional Content */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Metrics</h3>
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">CPU Usage</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">45%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Memory Usage</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">67%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '67%' }}></div>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Disk Usage</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">23%</span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '23%' }}></div>
                        </div>
                    </div>
                </div>

                <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Stats</h3>
                    <div className="space-y-3">
                        <div className="flex justify-between">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Active Sessions</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">1,247</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Page Views Today</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">45,678</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Bounce Rate</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">23.4%</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Avg. Session Duration</span>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">4m 32s</span>
                        </div>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminDashboardExample;
