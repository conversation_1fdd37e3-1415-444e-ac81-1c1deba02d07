import React from 'react';
import {
    Mail,
    Phone,
    MapPin,
    Facebook,
    Twitter,
    Linkedin,
    Instagram,
    ArrowRight
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';
const Footer = () => {
    const footerSections = [
        {
            title: 'Product',
            links: [
                { name: 'Features', href: '#features' },
                { name: 'Pricing', href: '#pricing' },
                { name: 'Integrations', href: '#integrations' },
                { name: 'API', href: '#api' },
                { name: 'Security', href: '#security' }
            ]
        },
        {
            title: 'Solutions',
            links: [
                { name: 'Small Business', href: '#small-business' },
                { name: 'Enterprise', href: '#enterprise' },
                { name: 'Sales Teams', href: '#sales' },
                { name: 'Marketing', href: '#marketing' },
                { name: 'Customer Support', href: '#support' }
            ]
        },
        {
            title: 'Resources',
            links: [
                { name: 'Documentation', href: '#docs' },
                { name: 'Help Center', href: '#help' },
                { name: 'Blog', href: '#blog' },
                { name: 'Webinars', href: '#webinars' },
                { name: 'Case Studies', href: '#cases' }
            ]
        },
        {
            title: 'Company',
            links: [
                { name: 'About Us', href: '#about' },
                { name: 'Careers', href: '#careers' },
                { name: 'Press', href: '#press' },
                { name: 'Partners', href: '#partners' },
                { name: 'Contact', href: '#contact' }
            ]
        }
    ];
    const socialLinks = [
        { icon: Facebook, href: '#', label: 'Facebook' },
        { icon: Twitter, href: '#', label: 'Twitter' },
        { icon: Linkedin, href: '#', label: 'LinkedIn' },
        { icon: Instagram, href: '#', label: 'Instagram' }
    ];
    const handleLinkClick = (href) => {
        if (href.startsWith('#')) {
            const element = document.querySelector(href);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }
    };
    return (
        <footer className="bg-gray-900 text-white">
            {}
            <div className="border-t border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                            <p className="text-gray-400 text-sm">
                                © 2024 Accord. All rights reserved.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    );
};
export default Footer;
