# AdminLayout Integration Guide

This guide shows how to integrate the AdminLayout into your microservices repositories like `Saas-Crm-User-Front` or other similar projects.

## Quick Start

### 1. Install the Package

In your microservice repository:

```bash
# Navigate to your microservice project
cd /path/to/Saas-Crm-User-Front

# Install the layouts package
npm install file:../Saas-CRM-Frontend/packages/layouts

# Or if using yarn
yarn add file:../Saas-CRM-Frontend/packages/layouts
```

### 2. Import and Use

```jsx
// src/App.jsx or your main layout file
import React from 'react';
import { AdminLayout } from '@saas-crm/layouts';
import '@saas-crm/layouts/AdminLayout.css'; // Import styles
import { BrowserRouter, Routes, Route } from 'react-router-dom';

// Your page components
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Settings from './pages/Settings';

function App() {
  return (
    <BrowserRouter>
      <AdminLayout
        title="User Management"
        subtitle="Admin Portal"
        user={{
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'Administrator'
        }}
        onLogout={() => {
          // Handle logout logic
          localStorage.removeItem('token');
          window.location.href = '/login';
        }}
      >
        <Routes>
          <Route path="/admin/dashboard" element={<Dashboard />} />
          <Route path="/admin/users" element={<Users />} />
          <Route path="/admin/settings" element={<Settings />} />
        </Routes>
      </AdminLayout>
    </BrowserRouter>
  );
}

export default App;
```

### 3. Create Your Admin Pages

```jsx
// src/pages/Dashboard.jsx
import React from 'react';
import { 
  AdminPageHeader, 
  AdminStatsCard, 
  AdminQuickActions 
} from '@saas-crm/layouts';
import { Users, DollarSign, ShoppingCart, Activity } from 'lucide-react';

function Dashboard() {
  return (
    <div>
      <AdminPageHeader
        title="User Management Dashboard"
        subtitle="Monitor user activity and system performance"
        breadcrumbs={[
          { label: 'Admin', href: '/admin' },
          { label: 'Dashboard' }
        ]}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <AdminStatsCard
          title="Total Users"
          value="1,247"
          change="+12%"
          changeType="positive"
          icon={Users}
          color="primary"
        />
        <AdminStatsCard
          title="Active Sessions"
          value="89"
          change="+5%"
          changeType="positive"
          icon={Activity}
          color="success"
        />
        <AdminStatsCard
          title="New Registrations"
          value="23"
          change="+18%"
          changeType="positive"
          icon={ShoppingCart}
          color="warning"
        />
        <AdminStatsCard
          title="Revenue"
          value="$12,450"
          change="+8%"
          changeType="positive"
          icon={DollarSign}
          color="secondary"
        />
      </div>

      <AdminQuickActions
        actions={[
          { label: 'Add User', href: '/admin/users/new', color: 'primary' },
          { label: 'Export Data', href: '/admin/export', color: 'secondary' },
          { label: 'System Backup', href: '/admin/backup', color: 'success' },
          { label: 'View Reports', href: '/admin/reports', color: 'warning' },
        ]}
      />
    </div>
  );
}

export default Dashboard;
```

## Advanced Configuration

### Custom Navigation

```jsx
// src/config/navigation.js
import { 
  Home, Users, Settings, BarChart3, Shield, 
  Database, FileText, Mail, Bell 
} from 'lucide-react';

export const navigationItems = [
  {
    category: 'Overview',
    items: [
      { href: '/admin/dashboard', label: 'Dashboard', icon: Home },
      { href: '/admin/analytics', label: 'Analytics', icon: BarChart3, badge: 'New' },
    ]
  },
  {
    category: 'User Management',
    items: [
      { href: '/admin/users', label: 'Users', icon: Users, badge: 12 },
      { href: '/admin/roles', label: 'Roles', icon: Shield },
    ]
  },
  {
    category: 'System',
    items: [
      { href: '/admin/settings', label: 'Settings', icon: Settings },
      { href: '/admin/database', label: 'Database', icon: Database },
    ]
  }
];
```

### Theme Integration

```jsx
// src/hooks/useTheme.js
import { useState, useEffect } from 'react';

export const useTheme = () => {
  const [theme, setTheme] = useState(() => {
    return localStorage.getItem('admin-theme') || 'light';
  });

  useEffect(() => {
    localStorage.setItem('admin-theme', theme);
    document.documentElement.classList.toggle('dark', theme === 'dark');
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  return { theme, toggleTheme };
};

// Usage in App.jsx
import { useTheme } from './hooks/useTheme';

function App() {
  const { theme, toggleTheme } = useTheme();

  return (
    <AdminLayout
      theme={theme}
      onThemeToggle={toggleTheme}
      // ... other props
    >
      {/* Your routes */}
    </AdminLayout>
  );
}
```

### Authentication Integration

```jsx
// src/contexts/AuthContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session
    const token = localStorage.getItem('token');
    if (token) {
      // Validate token and get user info
      fetchUserInfo(token).then(setUser).finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  }, []);

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
    window.location.href = '/login';
  };

  return (
    <AuthContext.Provider value={{ user, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Usage in App.jsx
import { AuthProvider, useAuth } from './contexts/AuthContext';

function AppContent() {
  const { user, logout, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <LoginPage />;
  }

  return (
    <AdminLayout
      user={user}
      onLogout={logout}
      // ... other props
    >
      {/* Your routes */}
    </AdminLayout>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}
```

## Styling Customization

### Custom CSS Variables

```css
/* src/styles/admin-theme.css */
:root {
  /* Override default colors */
  --color-primary-600: #7c3aed; /* Purple theme */
  --color-primary-700: #6d28d9;
  
  /* Custom sidebar width */
  --admin-sidebar-width: 18rem;
  --admin-sidebar-collapsed-width: 5rem;
  
  /* Custom fonts */
  --font-family-admin: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Custom component styles */
.admin-layout {
  font-family: var(--font-family-admin);
}

.admin-nav-item.active {
  background: linear-gradient(90deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
}
```

### Tailwind Configuration

```js
// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./node_modules/@saas-crm/layouts/**/*.{js,jsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

## Environment-Specific Configuration

```jsx
// src/config/admin.js
const config = {
  development: {
    title: 'Dev Admin Portal',
    subtitle: 'Development Environment',
    theme: 'light',
  },
  staging: {
    title: 'Staging Admin Portal',
    subtitle: 'Staging Environment',
    theme: 'light',
  },
  production: {
    title: 'Admin Portal',
    subtitle: 'Production Environment',
    theme: 'light',
  }
};

export const getAdminConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return config[env];
};

// Usage
import { getAdminConfig } from './config/admin';

function App() {
  const adminConfig = getAdminConfig();
  
  return (
    <AdminLayout
      title={adminConfig.title}
      subtitle={adminConfig.subtitle}
      theme={adminConfig.theme}
      // ... other props
    />
  );
}
```

## Best Practices

1. **Consistent Navigation**: Use the same navigation structure across all your microservices
2. **Shared State**: Consider using a state management solution like Redux or Zustand for shared admin state
3. **Error Boundaries**: Wrap your admin pages with error boundaries
4. **Loading States**: Implement proper loading states for better UX
5. **Responsive Design**: Test on different screen sizes
6. **Accessibility**: Ensure keyboard navigation and screen reader support

## Troubleshooting

### Common Issues

1. **Styles not loading**: Make sure to import the CSS file
2. **Icons not showing**: Ensure lucide-react is installed
3. **Navigation not working**: Check your routing setup
4. **Theme not persisting**: Implement proper theme persistence

### Performance Tips

1. **Lazy Loading**: Use React.lazy for page components
2. **Code Splitting**: Split your admin routes
3. **Memoization**: Use React.memo for expensive components
4. **Virtual Scrolling**: For large data tables

## Support

For issues or questions:
1. Check the main documentation in `AdminLayout.md`
2. Look at the example in `examples/AdminLayoutExample.jsx`
3. Review the component source code
4. Create an issue in the main repository
