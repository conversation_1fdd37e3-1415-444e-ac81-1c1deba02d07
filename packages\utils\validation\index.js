export const validation = {
    email: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    password: (password) => {
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    },
    passwordStrength: (password) => {
        let score = 0;
        let feedback = [];
        if (password.length >= 8) score += 1;
        else feedback.push('At least 8 characters');
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('One lowercase letter');
        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('One uppercase letter');
        if (/\d/.test(password)) score += 1;
        else feedback.push('One number');
        if (/[@$!%*?&]/.test(password)) score += 1;
        else feedback.push('One special character');
        const strength = score <= 2 ? 'weak' : score <= 3 ? 'medium' : score <= 4 ? 'strong' : 'very-strong';
        return { score, strength, feedback };
    },
    name: (name) => {
        return name && name.trim().length >= 2;
    },
    phone: (phone) => {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
};
